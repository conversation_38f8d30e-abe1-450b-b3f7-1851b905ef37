import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { runLearningAgent } from '@/lib/ai-agent/learning-agent';
import { LearningSession, LearningPhase, UserResponseType } from '@/lib/ai-agent/types';

export async function POST(request: NextRequest) {
  try {
    const { 
      message, 
      sessionId, 
      studySessionId,
      context 
    } = await request.json();

    if (!message) {
      return NextResponse.json({ error: 'Message is required' }, { status: 400 });
    }

    // Get or create learning session
    const learningSession = await getOrCreateLearningSession(sessionId, studySessionId);

    // Get user context
    const userContext = await getUserContext(learningSession.userId);

    // Prepare input for learning agent
    const agentInput = {
      userMessage: message,
      sessionId: learningSession.id,
      currentSession: learningSession,
      curriculum: learningSession.curriculum,
      phase: learningSession.currentPhase,
      context: {
        previousMessages: context?.previousMessages || [],
        userPreferences: userContext.preferences,
        learningHistory: userContext.history,
        ...context,
      },
    };

    // Run learning agent
    const agentResponse = await runLearningAgent(agentInput);

    // Update learning session based on agent response
    const sessionUpdate = await updateLearningSession(
      learningSession, 
      agentResponse, 
      message
    );

    // Save user interaction
    await saveUserInteraction(learningSession.id, message, context);

    return NextResponse.json({
      response: agentResponse.response,
      phase: agentResponse.phase,
      agentMessage: agentResponse.agentMessage,
      session: sessionUpdate,
      suggestedActions: agentResponse.suggestedActions || [],
      confidence: agentResponse.confidence || 0.8,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('AI Agent API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Helper functions
async function getOrCreateLearningSession(sessionId?: string, studySessionId?: string) {
  if (sessionId) {
    // Try to find existing learning session
    const existingSession = await db.learningSession.findUnique({
      where: { id: sessionId },
      include: {
        curriculum: true,
        userResponses: {
          orderBy: { timestamp: 'desc' },
          take: 10,
        },
      },
    });

    if (existingSession) {
      return existingSession;
    }
  }

  // Create new learning session
  const userId = 'temp-user-id'; // TODO: Replace with actual user ID from auth
  
  const newSession = await db.learningSession.create({
    data: {
      userId,
      studySessionId,
      currentPhase: LearningPhase.PLANNING,
      currentStepIndex: 0,
      progress: 0,
      startTime: new Date(),
      adaptiveDifficulty: 'intermediate',
    },
    include: {
      curriculum: true,
      userResponses: {
        orderBy: { timestamp: 'desc' },
        take: 10,
      },
    },
  });

  return newSession;
}

async function getUserContext(userId: string) {
  // Get user preferences and learning history
  const user = await db.user.findUnique({
    where: { id: userId },
    include: {
      studyGoals: true,
      studyProgress: true,
    },
  });

  return {
    preferences: {
      goals: user?.studyGoals.map(goal => goal.title) || [],
      difficulty: user?.studyProgress[0]?.level || 'intermediate',
      timeAvailable: '2 hours', // Default, could be user preference
    },
    history: {
      priorKnowledge: '', // Could be extracted from previous sessions
      completedTopics: user?.studyProgress.map(progress => progress.topicId) || [],
      learningStyle: 'visual', // Default, could be user preference
    },
  };
}

async function updateLearningSession(
  session: any, 
  agentResponse: any, 
  userMessage: string
) {
  const { phase, sessionUpdate } = agentResponse;
  
  // Calculate new progress if curriculum exists
  let progress = session.progress;
  if (sessionUpdate?.curriculum) {
    const completedSteps = sessionUpdate.currentStepIndex + 1;
    progress = Math.round((completedSteps / sessionUpdate.curriculum.steps.length) * 100);
  }

  // Update session
  const updatedSession = await db.learningSession.update({
    where: { id: session.id },
    data: {
      currentPhase: phase,
      currentStepIndex: sessionUpdate?.currentStepIndex || session.currentStepIndex,
      progress: progress,
      adaptiveDifficulty: sessionUpdate?.adaptiveDifficulty || session.adaptiveDifficulty,
      curriculumId: sessionUpdate?.curriculumId || session.curriculumId,
      updatedAt: new Date(),
      // Mark as completed if progress is 100%
      isCompleted: progress >= 100,
      endTime: progress >= 100 ? new Date() : null,
    },
    include: {
      curriculum: true,
      userResponses: {
        orderBy: { timestamp: 'desc' },
        take: 10,
      },
    },
  });

  // Save curriculum if provided
  if (sessionUpdate?.curriculum) {
    await saveCurriculum(sessionUpdate.curriculum);
  }

  return updatedSession;
}

async function saveCurriculum(curriculum: any) {
  try {
    // Check if curriculum already exists
    const existing = await db.curriculum.findUnique({
      where: { id: curriculum.id },
    });

    if (existing) {
      // Update existing curriculum
      await db.curriculum.update({
        where: { id: curriculum.id },
        data: {
          topic: curriculum.topic,
          subject: curriculum.subject,
          description: curriculum.description,
          difficulty: curriculum.difficulty,
          estimatedTotalTime: curriculum.estimatedTotalTime,
          learningObjectives: curriculum.learningObjectives,
          prerequisites: curriculum.prerequisites,
          steps: curriculum.steps,
          updatedAt: new Date(),
        },
      });
    } else {
      // Create new curriculum
      await db.curriculum.create({
        data: {
          id: curriculum.id,
          topic: curriculum.topic,
          subject: curriculum.subject,
          description: curriculum.description,
          difficulty: curriculum.difficulty,
          estimatedTotalTime: curriculum.estimatedTotalTime,
          learningObjectives: curriculum.learningObjectives,
          prerequisites: curriculum.prerequisites,
          steps: curriculum.steps,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      });
    }
  } catch (error) {
    console.error('Error saving curriculum:', error);
  }
}

async function saveUserInteraction(
  sessionId: string, 
  message: string, 
  context: any
) {
  try {
    // Determine response type based on message content
    let responseType = UserResponseType.PROCEED;
    
    if (message.includes('?') || message.toLowerCase().includes('question')) {
      responseType = UserResponseType.QUESTION;
    } else if (message.toLowerCase().includes('explain') || message.toLowerCase().includes('clarify')) {
      responseType = UserResponseType.CLARIFICATION;
    } else if (message.toLowerCase().includes('skip')) {
      responseType = UserResponseType.SKIP;
    } else if (message.toLowerCase().includes('repeat') || message.toLowerCase().includes('again')) {
      responseType = UserResponseType.REPEAT;
    } else if (message.toLowerCase().includes('help')) {
      responseType = UserResponseType.HELP;
    }

    await db.userInteraction.create({
      data: {
        sessionId,
        message,
        responseType,
        timestamp: new Date(),
      },
    });
  } catch (error) {
    console.error('Error saving user interaction:', error);
  }
}

// GET endpoint to retrieve learning session
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('sessionId');
    const studySessionId = searchParams.get('studySessionId');

    if (!sessionId && !studySessionId) {
      return NextResponse.json({ error: 'Session ID or Study Session ID is required' }, { status: 400 });
    }

    let learningSession;
    
    if (sessionId) {
      learningSession = await db.learningSession.findUnique({
        where: { id: sessionId },
        include: {
          curriculum: true,
          userResponses: {
            orderBy: { timestamp: 'desc' },
            take: 20,
          },
        },
      });
    } else if (studySessionId) {
      learningSession = await db.learningSession.findFirst({
        where: { studySessionId },
        include: {
          curriculum: true,
          userResponses: {
            orderBy: { timestamp: 'desc' },
            take: 20,
          },
        },
        orderBy: { startTime: 'desc' },
      });
    }

    if (!learningSession) {
      return NextResponse.json({ error: 'Learning session not found' }, { status: 404 });
    }

    return NextResponse.json({
      session: learningSession,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Get learning session error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}